/**
 * AlertCard Molecule - Alert Display Component
 * 
 * Atomic design alert card molecule combining atoms to display
 * contextual alerts with electrical engineering context.
 * 
 * Features:
 * - Atomic design principles (combining atoms)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized
 * - Consistent design system integration
 * - Professional electrical design standards
 * - Multiple alert variants for different contexts
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import { X, AlertTriangle, AlertCircle, CheckCircle2, Info, Zap } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/atoms/Button"
import { StatusIndicator } from "@/components/atoms/StatusIndicator"

// Alert card variants using CVA for consistent styling
const alertCardVariants = cva(
  "relative rounded-lg border p-4 transition-all duration-200",
  {
    variants: {
      variant: {
        default: "border-border bg-background text-foreground",
        info: "border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-950/50 dark:text-blue-400",
        success: "border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950/50 dark:text-green-400",
        warning: "border-amber-200 bg-amber-50 text-amber-800 dark:border-amber-800 dark:bg-amber-950/50 dark:text-amber-400",
        danger: "border-red-200 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-950/50 dark:text-red-400",
        // Electrical variants
        electrical: "border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-950/50 dark:text-yellow-400",
        fault: "border-red-200 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-950/50 dark:text-red-400",
        normal: "border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950/50 dark:text-green-400",
      },
      size: {
        sm: "p-3 text-sm",
        md: "p-4 text-sm",
        lg: "p-6 text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
)

// Icon mapping for alert variants
const variantIconMap = {
  default: Info,
  info: Info,
  success: CheckCircle2,
  warning: AlertTriangle,
  danger: AlertCircle,
  electrical: Zap,
  fault: AlertCircle,
  normal: CheckCircle2,
} as const

export interface AlertCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof alertCardVariants> {
  /** Alert title */
  title?: string
  /** Alert description/content */
  description?: string
  /** Children content (alternative to description) */
  children?: React.ReactNode
  /** Show alert icon */
  showIcon?: boolean
  /** Custom icon component */
  icon?: React.ComponentType<{ className?: string }>
  /** Show dismiss button */
  dismissible?: boolean
  /** Dismiss callback */
  onDismiss?: () => void
  /** Action buttons */
  actions?: React.ReactNode
  /** Show status indicator instead of default icon */
  showStatusIndicator?: boolean
  /** Status for status indicator */
  status?: "operational" | "warning" | "critical" | "energized" | "fault" | "offline" | "maintenance" | "active" | "inactive" | "pending"
  /** Animate on mount */
  animate?: boolean
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const AlertCard = React.forwardRef<HTMLDivElement, AlertCardProps>(
  (
    {
      variant = "default",
      size = "md",
      title,
      description,
      children,
      showIcon = true,
      icon: CustomIcon,
      dismissible = false,
      onDismiss,
      actions,
      showStatusIndicator = false,
      status,
      animate = false,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const [isVisible, setIsVisible] = React.useState(true)
    
    const IconComponent = CustomIcon || variantIconMap[variant || "default"]
    
    const handleDismiss = React.useCallback(() => {
      setIsVisible(false)
      setTimeout(() => {
        onDismiss?.()
      }, 150) // Allow animation to complete
    }, [onDismiss])
    
    if (!isVisible) {
      return null
    }
    
    // Map variant to status indicator variant
    const statusVariant = React.useMemo(() => {
      // If explicit status is provided, use it
      if (status) {
        return status
      }
      
      // Otherwise map variant to status
      const mapping: Record<string, any> = {
        success: "operational",
        warning: "warning", 
        danger: "critical",
        electrical: "energized",
        fault: "fault",
        normal: "operational",
        info: "operational",
        default: "operational",
      }
      return mapping[variant || "default"] || "operational"
    }, [variant, status])
    
    return (
      <div
        ref={ref}
        className={cn(
          alertCardVariants({ variant, size }),
          animate && "animate-in slide-in-from-top-2 duration-300",
          !isVisible && "animate-out slide-out-to-top-2 duration-150",
          className
        )}
        role="alert"
        data-testid={testId || `alert-card-${variant}`}
        {...props}
      >
        <div className="flex items-start gap-3">
          {/* Icon or Status Indicator */}
          {(showIcon || showStatusIndicator) && (
            <div className="flex-shrink-0">
              {showStatusIndicator && status ? (
                <StatusIndicator
                  variant={statusVariant}
                  size="md"
                  showIcon
                  showDot={false}
                  className="mt-0.5"
                />
              ) : showIcon && IconComponent ? (
                <IconComponent className="h-5 w-5 mt-0.5" aria-hidden="true" />
              ) : null}
            </div>
          )}
          
          {/* Content */}
          <div className="flex-1 min-w-0">
            {title && (
              <h3 className="font-semibold mb-1">
                {title}
              </h3>
            )}
            
            {description && (
              <div className="text-sm opacity-90">
                {description}
              </div>
            )}
            
            {children && !description && (
              <div className="text-sm opacity-90">
                {children}
              </div>
            )}
            
            {/* Actions */}
            {actions && (
              <div className="mt-3 flex items-center gap-2">
                {actions}
              </div>
            )}
          </div>
          
          {/* Dismiss button */}
          {dismissible && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="flex-shrink-0 h-6 w-6 p-0 hover:bg-black/5 dark:hover:bg-white/10"
              aria-label="Dismiss alert"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    )
  }
)

AlertCard.displayName = "AlertCard"

// Convenience components for common alert types
export const InfoAlertCard = React.forwardRef<HTMLDivElement, Omit<AlertCardProps, "variant">>(
  (props, ref) => <AlertCard ref={ref} variant="info" {...props} />
)
InfoAlertCard.displayName = "InfoAlertCard"

export const SuccessAlertCard = React.forwardRef<HTMLDivElement, Omit<AlertCardProps, "variant">>(
  (props, ref) => <AlertCard ref={ref} variant="success" {...props} />
)
SuccessAlertCard.displayName = "SuccessAlertCard"

export const WarningAlertCard = React.forwardRef<HTMLDivElement, Omit<AlertCardProps, "variant">>(
  (props, ref) => <AlertCard ref={ref} variant="warning" {...props} />
)
WarningAlertCard.displayName = "WarningAlertCard"

export const DangerAlertCard = React.forwardRef<HTMLDivElement, Omit<AlertCardProps, "variant">>(
  (props, ref) => <AlertCard ref={ref} variant="danger" {...props} />
)
DangerAlertCard.displayName = "DangerAlertCard"

export const ElectricalAlertCard = React.forwardRef<HTMLDivElement, Omit<AlertCardProps, "variant">>(
  (props, ref) => <AlertCard ref={ref} variant="electrical" {...props} />
)
ElectricalAlertCard.displayName = "ElectricalAlertCard"

export const FaultAlertCard = React.forwardRef<HTMLDivElement, Omit<AlertCardProps, "variant">>(
  (props, ref) => <AlertCard ref={ref} variant="fault" {...props} />
)
FaultAlertCard.displayName = "FaultAlertCard"

// Export types for external use
export type AlertCardVariant = NonNullable<AlertCardProps["variant"]>
export type AlertCardSize = NonNullable<AlertCardProps["size"]>

// Export variants for external use
export { alertCardVariants, variantIconMap }

// Utility functions for electrical context
export const getElectricalAlertVariant = (condition: string): AlertCardVariant => {
  const conditionMap: Record<string, AlertCardVariant> = {
    fault: "fault",
    alarm: "danger",
    warning: "warning",
    normal: "normal",
    energized: "electrical",
    overload: "warning",
    undervoltage: "warning",
    ok: "success",
    info: "info",
  }
  return conditionMap[condition.toLowerCase()] || "default"
}

export const createElectricalAlert = (params: {
  condition: string
  equipment: string
  message: string
  severity: "low" | "medium" | "high" | "critical"
}) => {
  const variant = getElectricalAlertVariant(params.condition)
  const title = `${params.equipment} ${params.condition.toUpperCase()}`
  
  return {
    variant,
    title,
    description: params.message,
    showStatusIndicator: true,
    status: params.condition.toLowerCase() as AlertCardProps["status"],
    animate: params.severity === "critical",
  }
}