/**
 * ProgressBar Atom - Progress Visualization Component
 * 
 * Atomic design progress bar component providing visual progress feedback
 * for electrical system operations with engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with smooth animations
 * - Consistent design system integration
 * - Professional electrical design standards
 * - Multiple variants for different contexts
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import { CheckCircle2, AlertTriangle, XCircle, Zap, Activity } from "lucide-react"

import { cn } from "@/lib/utils"

// Progress bar variants using CVA for consistent styling
const progressBarVariants = cva(
  "relative w-full overflow-hidden rounded-full bg-muted transition-all duration-300",
  {
    variants: {
      size: {
        xs: "h-1",
        sm: "h-2",
        md: "h-3",
        lg: "h-4",
        xl: "h-6",
      },
      variant: {
        default: "bg-muted",
        success: "bg-green-100 dark:bg-green-950/50",
        warning: "bg-amber-100 dark:bg-amber-950/50", 
        danger: "bg-red-100 dark:bg-red-950/50",
        info: "bg-blue-100 dark:bg-blue-950/50",
        // Electrical context variants
        power: "bg-emerald-100 dark:bg-emerald-950/50",
        voltage: "bg-blue-100 dark:bg-blue-950/50",
        current: "bg-purple-100 dark:bg-purple-950/50",
        energy: "bg-yellow-100 dark:bg-yellow-950/50",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "default",
    },
  }
)

// Progress fill variants
const progressFillVariants = cva(
  "h-full transition-all duration-500 ease-out rounded-full relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-primary",
        success: "bg-green-500", 
        warning: "bg-amber-500",
        danger: "bg-red-500",
        info: "bg-blue-500",
        // Electrical context variants
        power: "bg-emerald-500",
        voltage: "bg-blue-500", 
        current: "bg-purple-500",
        energy: "bg-yellow-500",
      },
      animated: {
        true: "bg-gradient-to-r animate-pulse",
        false: "",
      },
    },
    compoundVariants: [
      // Animated gradient variants
      {
        variant: "default",
        animated: true,
        className: "from-primary via-primary/80 to-primary",
      },
      {
        variant: "success", 
        animated: true,
        className: "from-green-500 via-green-400 to-green-500",
      },
      {
        variant: "power",
        animated: true,
        className: "from-emerald-500 via-emerald-400 to-emerald-500", 
      },
      {
        variant: "voltage",
        animated: true,
        className: "from-blue-500 via-blue-400 to-blue-500",
      },
    ],
    defaultVariants: {
      variant: "default",
      animated: false,
    },
  }
)

// Shimmer effect for loading states
const shimmerVariants = cva(
  "absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite]",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-r from-transparent via-primary/30 to-transparent",
        success: "bg-gradient-to-r from-transparent via-green-300/30 to-transparent",
        warning: "bg-gradient-to-r from-transparent via-amber-300/30 to-transparent",
        danger: "bg-gradient-to-r from-transparent via-red-300/30 to-transparent",
        info: "bg-gradient-to-r from-transparent via-blue-300/30 to-transparent",
        power: "bg-gradient-to-r from-transparent via-emerald-300/30 to-transparent",
        voltage: "bg-gradient-to-r from-transparent via-blue-300/30 to-transparent",
        current: "bg-gradient-to-r from-transparent via-purple-300/30 to-transparent",
        energy: "bg-gradient-to-r from-transparent via-yellow-300/30 to-transparent",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

// Format value for display
const formatValue = (value: number, unit?: string, precision?: number): string => {
  const rounded = precision !== undefined ? value.toFixed(precision) : value.toString()
  return unit ? `${rounded}${unit}` : rounded
}

// Get status icon based on progress value
const getStatusIcon = (value: number, variant: string) => {
  if (value >= 100) {
    return variant === "danger" ? XCircle : CheckCircle2
  } else if (value >= 75) {
    return variant === "power" ? Zap : Activity
  } else if (value >= 25) {
    return Activity
  } else {
    return variant === "danger" ? XCircle : AlertTriangle
  }
}

export interface ProgressBarProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof progressBarVariants> {
  /** Progress value (0-100) */
  value: number
  /** Maximum value (default: 100) */
  max?: number
  /** Minimum value (default: 0) */
  min?: number
  /** Display label */
  label?: string
  /** Show percentage value */
  showValue?: boolean
  /** Show status icon */
  showIcon?: boolean
  /** Unit to display with value */
  unit?: string
  /** Precision for decimal places */
  precision?: number
  /** Enable animated progress fill */
  animated?: boolean
  /** Enable shimmer loading effect */
  shimmer?: boolean
  /** Indeterminate loading state */
  indeterminate?: boolean
  /** Additional information text */
  helperText?: string
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const ProgressBar = React.forwardRef<HTMLDivElement, ProgressBarProps>(
  (
    {
      variant = "default",
      size = "md",
      value,
      max = 100,
      min = 0,
      label,
      showValue = false,
      showIcon = false,
      unit = "%",
      precision = 0,
      animated = false,
      shimmer = false,
      indeterminate = false,
      helperText,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    // Clamp value between min and max
    const clampedValue = Math.max(min, Math.min(max, value))
    const percentage = ((clampedValue - min) / (max - min)) * 100
    
    const StatusIcon = React.useMemo(() => getStatusIcon(percentage, variant || "default"), [percentage, variant])
    
    const displayValue = React.useMemo(() => {
      if (unit === "%") {
        return formatValue(percentage, unit, precision)
      } else {
        return formatValue(clampedValue, unit, precision)
      }
    }, [clampedValue, percentage, unit, precision])
    
    return (
      <div
        ref={ref}
        className={cn("w-full space-y-2", className)}
        data-testid={testId || "progress-bar"}
        {...props}
      >
        {/* Header with label and value */}
        {(label || showValue || showIcon) && (
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              {showIcon && (
                <StatusIcon 
                  className={cn(
                    "h-4 w-4",
                    percentage >= 100 ? (variant === "danger" ? "text-red-500" : "text-green-500") :
                    percentage >= 75 ? "text-blue-500" :
                    percentage >= 25 ? "text-amber-500" :
                    "text-red-500"
                  )}
                />
              )}
              {label && (
                <span className="font-medium text-foreground">
                  {label}
                </span>
              )}
            </div>
            {showValue && (
              <span className="font-mono text-muted-foreground">
                {displayValue}
              </span>
            )}
          </div>
        )}
        
        {/* Progress bar container */}
        <div
          className={cn(progressBarVariants({ variant, size }))}
          role="progressbar"
          aria-valuenow={indeterminate ? undefined : clampedValue}
          aria-valuemin={min}
          aria-valuemax={max}
          aria-label={label || "Progress"}
        >
          {/* Progress fill */}
          {!indeterminate && (
            <div
              className={cn(progressFillVariants({ variant, animated }))}
              style={{ width: `${percentage}%` }}
            >
              {/* Shimmer effect */}
              {shimmer && (
                <div className={cn(shimmerVariants({ variant }))} />
              )}
            </div>
          )}
          
          {/* Indeterminate animation */}
          {indeterminate && (
            <div
              className={cn(
                progressFillVariants({ variant, animated: true }),
                "w-1/3 animate-[indeterminate_1.5s_ease-in-out_infinite]"
              )}
            />
          )}
        </div>
        
        {/* Helper text */}
        {helperText && (
          <p className="text-xs text-muted-foreground">
            {helperText}
          </p>
        )}
      </div>
    )
  }
)

ProgressBar.displayName = "ProgressBar"

// Circular Progress variant
export interface CircularProgressProps {
  /** Progress variant */
  variant?: ProgressBarProps["variant"]
  /** Progress value (0-100) */
  value: number
  /** Maximum value (default: 100) */
  max?: number
  /** Minimum value (default: 0) */
  min?: number
  /** Display label */
  label?: string
  /** Show percentage value */
  showValue?: boolean
  /** Unit to display with value */
  unit?: string
  /** Precision for decimal places */
  precision?: number
  /** Enable indeterminate mode */
  indeterminate?: boolean
  /** Size in pixels */
  size?: number
  /** Stroke width */
  strokeWidth?: number
  /** CSS class name for the wrapper */
  className?: string
  /** Test identifier */
  "data-testid"?: string
}

export const CircularProgress = React.forwardRef<SVGSVGElement, CircularProgressProps>(
  (
    {
      variant = "default",
      value,
      max = 100,
      min = 0,
      size = 120,
      strokeWidth = 8,
      label,
      showValue = true,
      unit = "%",
      precision = 0,
      indeterminate = false,
      className,
      "data-testid": testId
    },
    ref
  ) => {
    const clampedValue = Math.max(min, Math.min(max, value))
    const percentage = ((clampedValue - min) / (max - min)) * 100
    
    const radius = (size - strokeWidth) / 2
    const circumference = radius * 2 * Math.PI
    const strokeDasharray = circumference
    const strokeDashoffset = circumference - (percentage / 100) * circumference
    
    const displayValue = React.useMemo(() => {
      if (unit === "%") {
        return formatValue(percentage, unit, precision)
      } else {
        return formatValue(clampedValue, unit, precision)
      }
    }, [clampedValue, percentage, unit, precision])
    
    const getStrokeColor = (variant: string): string => {
      const colorMap: Record<string, string> = {
        default: "hsl(var(--primary))",
        success: "hsl(142, 76%, 36%)",
        warning: "hsl(38, 92%, 50%)", 
        danger: "hsl(0, 84%, 60%)",
        info: "hsl(221, 83%, 53%)",
        power: "hsl(160, 84%, 39%)",
        voltage: "hsl(221, 83%, 53%)",
        current: "hsl(262, 83%, 58%)",
        energy: "hsl(45, 93%, 47%)",
      }
      return colorMap[variant] || colorMap.default
    }
    
    return (
      <div 
        className={cn("relative inline-flex items-center justify-center", className)}
        data-testid={testId || "circular-progress"}
      >
        <svg
          ref={ref}
          width={size}
          height={size}
          className="transform -rotate-90"
          {...svgProps}
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="hsl(var(--muted))"
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={getStrokeColor(variant || "default")}
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={indeterminate ? 0 : strokeDashoffset}
            className={cn(
              "transition-all duration-500 ease-out",
              indeterminate && "animate-spin"
            )}
            style={indeterminate ? {
              strokeDasharray: `${circumference * 0.25} ${circumference}`,
            } : undefined}
          />
        </svg>
        
        {/* Center content */}
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
          {showValue && !indeterminate && (
            <span className="text-lg font-semibold font-mono">
              {displayValue}
            </span>
          )}
          {label && (
            <span className="text-sm text-muted-foreground">
              {label}
            </span>
          )}
        </div>
      </div>
    )
  }
)

CircularProgress.displayName = "CircularProgress"

// Export types for external use
export type ProgressBarVariant = NonNullable<ProgressBarProps["variant"]>
export type ProgressBarSize = NonNullable<ProgressBarProps["size"]>

// Export variants for external use
export { progressBarVariants, progressFillVariants, shimmerVariants }

// Utility functions for electrical context
export const getElectricalProgressVariant = (type: string): ProgressBarVariant => {
  const typeMap: Record<string, ProgressBarVariant> = {
    power: "power",
    voltage: "voltage",
    current: "current", 
    energy: "energy",
    load: "warning",
    fault: "danger",
    normal: "success",
  }
  return typeMap[type] || "default"
}

export const getElectricalProgressColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    power: "text-emerald-600",
    voltage: "text-blue-600",
    current: "text-purple-600",
    energy: "text-yellow-600", 
  }
  return colorMap[type] || "text-gray-600"
}

// Custom keyframes for animations (add to global CSS)
export const progressAnimationKeyframes = `
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes indeterminate {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(0%); }
  100% { transform: translateX(100%); }
}
`